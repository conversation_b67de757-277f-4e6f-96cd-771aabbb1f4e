import * as DI from '@reolink-fx/di';
import * as LOG from '@reolink-fx/log';
import * as Config from '@reolink-fx/config';
import * as _ from '@reolink-fx/utils';
import * as Cache from '@reolink-fx/redis';
import { ConversationHandler } from './agents/conversation/ConversationHandler';
import { SunshineConversationClient, IContent } from './Clients/SunshineConversationClient';
import { TranslationService } from './agents/troubleshooting/TranslationService';

// 会话闲置检查器的配置选项
interface IInactiveSessionCheckerOptions {
    // 检查间隔(秒)
    checkInterval: number;
    // 闲置超时时间(分钟)
    inactiveTimeout: number;
    // 是否启用
    enabled: boolean;
    // 应用ID
    appId: string;
}

/**
 * 会话闲置检查器
 * 负责检查闲置的会话并发送提醒消息
 */
@DI.Singleton()
export class InactiveSessionChecker {
    private readonly _cache = Cache.useRedisClient();

    private readonly _logger = LOG.useLogger('InactiveSessionChecker');

    private readonly _conversationHandler = DI.use(ConversationHandler);

    private readonly _sunshineConversationClient = DI.use(SunshineConversationClient);

    private readonly _translationService = DI.use(TranslationService);

    private readonly _config = Config.useConfig<IInactiveSessionCheckerOptions>({
        'path': 'inactiveSessionChecker'
    });

    private readonly _inactiveReminderKeyPrefix = 'inactiveReminder:';

    public start(): void {
        if (!this._config.enabled) {
            this._logger.info({
                action: 'start',
                message: '会话闲置检查器已禁用'
            });
            return;
        }

        this._logger.info({
            action: 'start',
            message: '会话闲置检查器已启动',
            data: {
                checkInterval: this._config.checkInterval,
                inactiveTimeout: this._config.inactiveTimeout
            }
        });

        _.Async.invokeAsync(async () => {
            while (true) {
                try {
                    await this._checkInactiveSessions();
                }
                catch (error) {
                    this._logger.error({
                        action: '_checkInactiveSessions',
                        message: '检查闲置会话失败',
                        data: { error: JSON.stringify(error) }
                    });
                }
                await _.Async.sleep(this._config.checkInterval * 1000);
            }
        });
    }

    /**
     * 检查所有闲置会话
     */
    private async _checkInactiveSessions(): Promise<void> {
        // 获取所有会话最后消息时间的键
        const keys = await this._cache.keys('lastMessageTime:*');
        const now = Date.now();

        for (const key of keys) {
            try {
                const conversationId = key.replace('lastMessageTime:', '');

                // 检查是否是已经转人工的会话
                const isPassedToAgent = await this._conversationHandler.checkPassControlConversation(conversationId);
                if (isPassedToAgent) {
                    continue;
                }

                // 检查是否已经发送过提醒
                const reminderSent = await this._cache.get(`${this._inactiveReminderKeyPrefix}${conversationId}`);
                if (reminderSent) {
                    continue;
                }

                // 获取最后消息时间
                const lastMessageTime = await this._conversationHandler.getLastMessageTime(conversationId);
                if (!lastMessageTime) {
                    continue;
                }

                // 计算闲置时间(分钟)
                const inactiveMinutes = (now - lastMessageTime) / (1000 * 60);

                // 如果闲置时间超过配置的超时时间，发送提醒
                if (inactiveMinutes >= this._config.inactiveTimeout) {
                    await this._sendInactiveReminder(conversationId);

                    // 标记已发送提醒
                    await this._cache.setEX(
                        `${this._inactiveReminderKeyPrefix}${conversationId}`,
                        'true',
                        60 * 60 * 24
                    ); // 24小时过期
                }
            }
            catch (error) {
                this._logger.error({
                    action: '_checkInactiveSessions',
                    message: '处理会话闲置检查失败',
                    data: { key, error: JSON.stringify(error) }
                });
            }
        }
    }

    /**
     * 发送会话闲置提醒
     * @param conversationId 会话ID
     */
    private async _sendInactiveReminder(conversationId: string): Promise<void> {
        try {
            // 获取应用ID
            const appId = this._config.appId;

            // 获取最后一条用户消息用于语言检测
            const lastUserMessage = await this._conversationHandler.getLastUserMessageFromCache(conversationId);

            // 需要翻译的文本
            const defaultMessage = 'Hi there, we noticed that your session has been inactive for a while. May we kindly ask if you still require assistance?';
            const resolvedButtonText = 'Resolved';
            const continueButtonText = 'Continue Troubleshooting';
            const transferButtonText = 'Transfer to Agent';

            // 根据用户最后消息的语言翻译提醒消息和按钮文本
            const translatedTexts = await this._translationService.translateTexts(
                lastUserMessage ?? '',
                [defaultMessage, resolvedButtonText, continueButtonText, transferButtonText]
            );

            // 构造按钮选项
            const content: IContent = {
                type: 'text',
                markdownText: translatedTexts[0],
                actions: [
                    {
                        type: 'reply',
                        text: `1️⃣ ${translatedTexts[1]}`,
                        payload: 'resolved',
                        metadata: {
                            stepId: 'inactive_session'
                        }
                    },
                    {
                        type: 'reply',
                        text: `2️⃣ ${translatedTexts[2]}`,
                        payload: 'continue',
                        metadata: {
                            stepId: 'inactive_session'
                        }
                    },
                    {
                        type: 'reply',
                        text: `3️⃣ ${translatedTexts[3]}`,
                        payload: 'transfer',
                        metadata: {
                            stepId: 'inactive_session'
                        }
                    }
                ]
            };

            // 发送提醒消息
            await this._sunshineConversationClient.sendMessage(appId, conversationId, content);

            this._logger.info({
                action: '_sendInactiveReminder',
                message: '已发送会话闲置提醒',
                data: { conversationId }
            });
        }
        catch (error) {
            this._logger.error({
                action: '_sendInactiveReminder',
                message: '发送会话闲置提醒失败',
                data: { conversationId, error: JSON.stringify(error) }
            });
        }
    }
}
