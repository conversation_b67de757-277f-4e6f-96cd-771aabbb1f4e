import * as DI from '@reolink-fx/di';
import * as LOG from '@reolink-fx/log';
import * as Http from '@reolink-fx/http';
import * as Config from '@reolink-fx/config';
import * as _ from '@reolink-fx/utils';
import { E_GET_SWITCHBOARD_ERROR, E_TRANSFER_ERROR } from '#/Errors/Zendesk';
// import { agentState } from '#/Services/Decls/TroubleShootingFlow'; // Removed as it's no longer used
import { ConversationHandler } from '../conversation/ConversationHandler';
import { SunshineConversationClient, IContent } from '#/Services/Clients/SunshineConversationClient';
import { IPushedStep } from '#/DAO/Conversations';
import { TranslationService } from './TranslationService';
// import { IPushedStep } from '#/DAO/Conversations';

/**
 * 人工客服转接状态枚举
 */
export enum ETransferStatus {
    PENDING = 'pending',
    ACCEPTED = 'accepted',
    REJECTED = 'rejected',
    TIMEOUT = 'timeout'
}

/**
 * 转接配置接口
 */
export interface ITransferConfig {
    appId: string;
    conversationId: string;
    timeout?: number; // 超时时间，单位：毫秒
    metadata?: Record<string, any>; // 附加元数据
}

/**
 * 转接结果接口
 */
export interface ITransferResult {
    status: ETransferStatus;
    agentId?: string;
    message?: string;
}

/**
 * 人工客服转接工具类
 */
@DI.Singleton()
export class AgentController {
    private readonly _logger = LOG.useLogger('HumanAgentTransfer');

    private readonly _conversationHandler = DI.use(ConversationHandler);

    private readonly _sunshineConversationClient = DI.use(SunshineConversationClient);

    private readonly _translationService = DI.use(TranslationService);

    private readonly _config = Config.useConfig<{
        baseUrl: string;
        keyId: string;
        secret: string;
        pushedSopTicketFieldId: string;
    }>({
        'path': 'sunshineConversationClientOptions'
    });

    private _switchboardIntegrationId: string = '';

    /**
     * 格式化推送步骤为文本
     * @param pushedSteps 推送步骤数组
     * @returns 格式化后的文本
     */
    private _formatPushedSteps(pushedSteps: IPushedStep[]): string {
        if (!pushedSteps || pushedSteps.length === 0) {
            return 'no sop pushed';
        }

        return pushedSteps.map((sop, sopIndex) => {
            const stepsText = sop.steps.map((step, stepIndex) => {
                return `step ${stepIndex + 1}: ${step.step}`;
            }).join('\n\n');

            return `intent ${sopIndex + 1}: ${sop.intent}

${stepsText}`;
        }).join('\n\n');
    }

    /**
     * 初始化Switchboard集成ID
     * @param appId 应用ID
     */
    protected async _initSwitchboardIntegrationId(appId: string): Promise<void> {
        try {
            this._switchboardIntegrationId = await this._getSwitchboardIntegrationId(appId);
        }
        catch (error) {
            throw new E_GET_SWITCHBOARD_ERROR().addResponseMetadata({
                error,
                appId
            });
        }
    }

    /**
     * 处理转人工流程
     * @param appId 应用ID
     * @param conversationId 会话ID
     * @returns 响应信息和条件
     */
    public async handleTransferProcess(
        appId: string,
        conversationId: string,
        intent: string,
        isDoneStep: boolean
    ): Promise<string> {

        const lastUserMessage = await this._conversationHandler.getLastUserMessageFromCache(conversationId);

        try {
            // 获取用户转人工请求次数
            const transferRequestCount = await this._conversationHandler.getTransferRequestCount(conversationId) ?? 0;

            // 意图不为空，且为第一次转人工 则返回确认提示
            if (transferRequestCount === 0 && intent !== '' && !isDoneStep) {
                // 首次请求转人工，显示确认提示并记录次数
                await this._conversationHandler.incrementTransferRequestCount(conversationId);

                const confirmMsg = 'It looks like we\'re just a few steps away from completing the troubleshooting process. Are you sure you\'d like to switch to a live agent? Please note that our human support may take some time to respond. We\'re happy to assist you either way!';
                const [translatedMsg] = await this._translationService.translateTexts(
                    lastUserMessage ?? '',
                    [confirmMsg]
                );

                return translatedMsg;
            }
            else {
                // 后续请求，直接调用transfer
                await this.transfer(appId, conversationId, lastUserMessage ?? '', isDoneStep);
                return '';
            }
        }
        catch (error) {
            this._logger.error({
                action: 'handleTransferProcess',
                message: '处理转人工流程失败',
                data: { error, appId, conversationId }
            });

            // 发生错误时返回默认消息
            return 'Sorry, an error occurred while processing your request. Please try again later.';
        }
    }

    /**
     * 发起人工客服转接
     * @param config 转接配置
     */
    public async transfer(
        appId: string,
        conversationId: string,
        lastUserMessage: string,
        isDoneStep: boolean
    ): Promise<void> {
        try {
            this._logger.info({
                action: 'transfer',
                message: '开始转接人工客服',
                data: { appId, conversationId }
            });

            // 检查是否已收集用户信息
            const hasUserInfo = await this._conversationHandler.checkUserInfoCollected(conversationId);
            if (!hasUserInfo) {
                // 如果尚未收集用户信息，发送表单收集
                await this._sendUserInfoForm(appId, conversationId, lastUserMessage, isDoneStep);
                return;
            }

            if (!this._switchboardIntegrationId) {
                await this._initSwitchboardIntegrationId(appId);
            }

            const pushedSteps = await this._conversationHandler.getConversationPushedStep(conversationId);

            const firstMessageId = await this._conversationHandler.getFirstMessageFromCache(conversationId);

            const metadata: Record<string, any> = {};

            if (firstMessageId) {
                metadata['first_message_id'] = firstMessageId;
            }

            metadata[`dataCapture.ticketField.${this._config.pushedSopTicketFieldId}`] = this._formatPushedSteps(pushedSteps);

            await this._sunshineConversationClient.passControl(
                appId,
                conversationId,
                this._switchboardIntegrationId,
                metadata);

            await this._conversationHandler.saveConversationMetricsToCache(
                {
                    zdConversationId: conversationId,
                    isPassToAgent: 1,
                    isCreateTicket: 1
                });

            await this._conversationHandler.savePassControlConversation(conversationId);

            // 转人工后删除lastMessage缓存
            await this._conversationHandler.deleteLastUserMessageFromCache(conversationId);

        }
        catch (error) {
            this._logger.error({
                action: 'transfer',
                message: '人工客服转接失败',
                data: { error }
            });

            throw new E_TRANSFER_ERROR().addResponseMetadata({
                error
            });
        }
    }

    /**
     * Send user information collection form
     * @param appId App ID
     * @param conversationId Conversation ID
     */
    private async _sendUserInfoForm(
        appId: string,
        conversationId: string,
        lastUserMessage: string,
        isDoneStep: boolean
    ): Promise<void> {
        // 原始文本
        const doneStepPromptText = 'Thank you for working through the steps! While we’ve covered all troubleshooting options, it looks like we’ll need an expert agent to dive deeper. Could you share your email address? Our team will reach out via email to ensure your case gets the attention it deserves.';
        const transferPromptText = `Please provide the following details. As it is currently outside of our live messaging support hours, your inquiry will be forward to our email support team. We'll get back to you as soon as possible. Thank you for your patience and understanding!`;
        const nameLabel = 'Name';
        const namePlaceholder = 'Enter your name';
        const emailLabel = 'Email';
        const emailPlaceholder = 'Enter your email address';

        let promptText = '';

        if (isDoneStep) {
            promptText = doneStepPromptText;
        }
        else {
            promptText = transferPromptText;
        }
        // 翻译文本
        const [translatedPrompt, translatedNameLabel, translatedNamePlaceholder,
            translatedEmailLabel, translatedEmailPlaceholder] = await this._translationService.translateTexts(
            lastUserMessage,
            [promptText, nameLabel, namePlaceholder, emailLabel, emailPlaceholder]
        );

        // Send prompt message
        const message: IContent = {
            type: 'text',
            markdownText: translatedPrompt
        };
        await this._sunshineConversationClient.sendMessage(appId, conversationId, message);

        // Send form
        const userInfoForm: IContent = {
            type: 'form',
            blockChatInput: true,
            fields: [
                {
                    type: 'text',
                    name: 'userName',
                    label: translatedNameLabel,
                    placeholder: translatedNamePlaceholder,
                    required: true
                },
                {
                    type: 'email',
                    name: 'userEmail',
                    label: translatedEmailLabel,
                    placeholder: translatedEmailPlaceholder,
                    required: true
                }
            ]
        };
        await this._sunshineConversationClient.sendMessage(appId, conversationId, userInfoForm);
    }

    private async _getSwitchboardIntegrationId(appId: string): Promise<string> {

        const basicAuth = `Basic ${Buffer.from(`${this._config.keyId}:${this._config.secret}`).toString('base64')}`;

        // 1. 获取switchboardId
        const switchBoardResponse = await Http.hCli.request({
            url: `${this._config.baseUrl}/v2/apps/${appId}/switchboards`,
            method: 'GET',
            headers: {
                'Authorization': basicAuth,
                'Content-Type': 'application/json'
            },
            data: JSON.stringify({})
        });

        const switchBoardData = (await switchBoardResponse.getBuffer()).toString();

        const switchBoards = _.String.parseJSON<any>(switchBoardData, {
            onError: (error) => {
                this._logger.error({
                    action: 'parseJSON',
                    message: 'JSON解析失败',
                    data: { error }
                });
                return [];
            }
        });

        const switchBoardId: string = (switchBoards.switchboards as any[])
            .find((switchBoard) => switchBoard.enabled).id;

        // 2. 获取switchboardIntegrationId
        const switchBoardIntegrationResponse = await Http.hCli.request({
            url: `${this._config.baseUrl}/v2/apps/${appId}/switchboards/${switchBoardId}/switchboardIntegrations`,
            method: 'GET',
            headers: {
                'Authorization': basicAuth,
                'Content-Type': 'application/json'
            },
            data: JSON.stringify({})
        });

        const switchBoardIntegrationData = (await switchBoardIntegrationResponse.getBuffer()).toString();

        const switchBoardIntegrationsData = _.String.parseJSON<any>(switchBoardIntegrationData, {
            onError: (error) => {
                this._logger.error({
                    action: 'parseJSON',
                    message: 'JSON解析失败',
                    data: { error }
                });
                return [];
            }
        });

        const switchBoardIntegrationId: string = (switchBoardIntegrationsData.switchboardIntegrations as any[])
            .find((switchBoardIntegration) => switchBoardIntegration.integrationType === 'zd:agentWorkspace').id;

        return switchBoardIntegrationId;
    }
}
